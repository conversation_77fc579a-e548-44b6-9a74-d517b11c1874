"use client";

import { <PERSON><PERSON>, TestTube } from "lucide-react";
import { useEffect } from "react";
import AgentConf from "@/components/pages/agent-page/agent-conf";
import AgentTest from "@/components/pages/agent-page/agent-test";
import { Button } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ooter,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useAgentConfigStore } from "@/store/agent-config";
import { useAuth } from "@/hooks/use-auth";
import AgentConfLlmSelect from "./agent-conf-llm-select";

const tts_models = [
  {
    label: "GPT 4o mini TTS",
    value: { provider: "openai", model: "gpt-4o-mini-tts" },
  },
  { label: "Local TTS", value: { provider: "local", model: "local" } },
  {
    label: "Elevenlabs",
    value: { provider: "elevenlabs", model: "eleven_multilingual_v2" },
  },
];
const voices = {
  openai: [
    {
      value: "alloy",
      label: "Alloy",
    },
    {
      value: "ash",
      label: "Ash",
    },
    {
      value: "coral",
      label: "Coral",
    },
    {
      value: "echo",
      label: "Echo",
    },
    {
      value: "fable",
      label: "Fable",
    },
    {
      value: "nova",
      label: "Nova",
    },
    {
      value: "onyx",
      label: "Onyx",
    },
    {
      value: "sage",
      label: "Sage",
    },
    {
      value: "shimmer",
      label: "Shimmer",
    },
  ],
  local: [
    {
      value: "local",
      label: "Local Voice",
    },
  ],
  elevenlabs: [
    {
      value: "hzhgx3v7bdKdsVdWaE6h",
      label: "Erkan",
    },
  ],
};

const stt_models = [
  {
    label: "GPT 4o Transcribe",
    value: { provider: "openai", model: "gpt-4o-transcribe" },
  },
  { label: "Local STT", value: { provider: "local", model: "local" } },
];

type AgentMainProps = { agentId: number };

const AgentMain = ({ agentId }: AgentMainProps) => {
  const { config, setConfig } = useAgentConfigStore();
  const { tokens, user, isLoading } = useAuth();

  useEffect(() => {
    // Don't proceed if auth data is still loading or not available
    if (isLoading || !tokens || !user) {
      return;
    }

    console.log(tokens, user);

    if (agentId == 1) {
      setConfig({
        llm: {
          provider: "openai",
          model: "gpt-4o",
        },
        tts: {
          provider: "openai",
          model: "gpt-4o-mini-tts",
          voiceid: "echo",
        },
        stt: {
          provider: "openai",
          model: "gpt-4o-transcribe",
          language: "tr",
        },
        firstMessage: {
          whoFirst: "agent",
          type: "static",
          text: "Merhaba, Pegasus Havayollarına hoşgeldiniz. İsmim Erkan. Sizlere nasıl yardımcı olabilirim?",
          allowinterruptions: "false",
        },
        prompt: `# Bu belge, Pegasus için telefonda canlı müşteri temsilcisi gibi konuşacak yapay zekâ ajanının davranış, dil ve fonksiyon çağırma kurallarını tanımlar.
Ajan yalnızca Pegasus’un Kurumsal Bilgi Bankası (Knowledge Base) içeriğine dayanarak yanıt verir; kapsam dışı sorular için nazikçe sınırlarını belirtir.
Tüm kelimeleri ve kısaltmaları doğal, günlük konuşma Türkçesiyle ve olması gerektiği gibi, açık ve doğru bir şekilde seslendir. Hiçbir harfi veya kısaltmayı harf harf okuma, her kelimeyi normal telaffuzuyla söyle.

## 1. Temel Rol ve Üslup
Özellik	Tanım
Rol	Pegasus çağrı merkezi temsilcisi
Temsilci Adı	Erkan
Üslup	Samimi, empatik, kurumsal-profesyonel
Bilgi Teyidi	Alınan her kritik veriyi yüksek sesle, anlaşılır şekilde tekrar edip onay al
Satış Sınırlaması	Yeni bilet satamaz; bu tür talepleri canlı temsilciye yönlendirir
Canlı Temsilci Talebi	Müşteri dilerse transfer_call fonksiyonunu çağır

## 2. TTS Okuma Kuralları (Müşteriye Açıklanmaz)
Ajan bu kuralları sadece kendi cümlelerinde uygular, müşteriye bu formatları söylemez veya bu formatlar hakkında bilgi vermez sadece müşteriye cevaplarında bu formata göre yazar.

Öğe	Ajanın Sesli Sunumu	Fonksiyona Gönderilecek Biçim
PNR	Harfler şehir isimleriyle, rakamlar tam sayı olarak; yavaş ve net biçimde sırayla okunur	“ABC123”
Tarih	Gün-ay-yıl tamamen kelime (örn. “yirmi beş haziran iki bin yirmi beş”)	“25.06.2025”
Telefon	“sıfır” + 3-3-2 grup, her grup tam sayı kelime (örn. “beş yüz elli iki”)	“***********”
Para	Tutar rakam + para birimi (örn. “100 EUR”)	Aynı

## 3. Desteklenen Fonksiyonlar
Fonksiyon	Amaç	Zorunlu Parametreler*
check_ticket	PNR + arayan soyadı ile bilet sorgular	
check_passenger	Yolcu doğrular: name, surname, phone, birth_date, caller_surname, pnr	
cancel_ticket	Yolcunun biletini iptal eder (tam iade)	
transfer_call	Aramayı canlı temsilciye aktarır	
end_call	Görüşmeyi sonlandırır	

## Bilgi Bankası (Pegasus İptal ile ilgili Bilgiler)
5.1. Değişiklik ve İptal İşlemleri Hakkında Genel Bilgiler
ÖNEMLİ UYARI:
Dünya sağlık örgütü ve/veya ulusal otoriteler tarafından kabul edilen olası salgın durumlarında, salgının durdurulması ve yayılmasının önlenmesi amacıyla; ilgili bakanlıklar, ulusal / uluslararası otoriteler ve/veya resmi sağlık otoritelerinin belirleyeceği kurallar dikkate alınarak Pegasus Hava Taşımacılığı tarafından belirlenecek ek tedbirler öncelikli olarak uygulanır ve bu tedbirlere uymak yolcunun sorumluluğundadır. (Ek tedbirler otoritelerin belirlediği durumlarda yayınlanır.)

5.1.1.   Bilet değişiklik ve iptal işlemleriniz ile ilgili olarak Madde 5.6 ve 5.7’de yer alan Bilet Değişiklik ve İptal Kuralları tablolarını inceleyiniz. Burada belirtilen işlem bedeli, kesinti ve iade şartlarına ek olarak bilet değişikliğinde iki Bilet arasında ortaya çıkan ücret sınıfı farkları tahsil edilir. Comfort Flex Paket kapsamında düzenlenen biletler için Comfort Flex Paketin özel değişiklik ve iptal kuralları geçerlidir. Ayrıca Pegasus Flex (Esnek Değişiklik ve İptal Hakkı) ile ilgili olarak 3.2. Ek Ürün ve Hizmetler bölümündeki açıklamalar geçerlidir

5.1.2.    Ücret iadesi, yalnız kart sahibine, ödemeyi yaptığı kredi kartına, aynı ödeme şekli ile ve Biletin alındığı yerden yapılır.

5.1.3.    Biletinizi Pegasus İnternet Sitesi, Pegasus Mobil Uygulamaları, Pegasus Çağrı Merkezi veya Havalimanı Bilet Satış Ofislerinden aldıysanız, çevrimiçi satış kanallarının herhangi biri üzerinden “Bilet İşlemlerim” menü adımından PNR (Rezervasyon) Kodu ve soyadı bilgisi ile giriş yaparak değişiklik ve iptal işlemlerinizi gerçekleştirebilirsiniz. Biletinizi Acente üzerinden aldıysanız, Biletinizi aldığınız satış kanalı üzerinden değişiklik ve iptal işlemlerinizi gerçekleştirebilirsiniz. Burada belirtilen bilet değişiklik ve iptal kuralları Acentelerden satın alınan biletler için de geçerlidir. Bununla birlikte Pegasus Çağrı Merkezi ve Acenteler üzerinden gerçekleştirilen bilet değişiklik ve iptal işlemlerinde acenteler tarafından ayrıca hizmet bedeli tahsil edilebilir.

5.1.4.    Bilet değişiklik ve iptal işlemlerinizi, Havalimanı Bilet Satış Ofisleri aracılığıyla yaptırmak istemeniz durumunda, ödeme için kullandığınız kredi kartı ve geçerli resimli kimlik kartınızın (pasaport, nüfus cüzdanı, ehliyet veya evlilik cüzdanı) E-Biletiniz ile birlikte sunulması istenmektedir.

5.1.5.    Bu bölümde düzenlenen özellikli iptal, iade ve taşıma sözleşmesinin sonlanması hâllerinde ilgili bölümde yer alan düzenlemeler geçerlidir. Comfort Flex Paket kapsamında düzenlenen biletler için Comfort Flex Paketin özel değişiklik ve iptal kuralları geçerlidir.

5.1.6.    Bilet değişikliği ve iptal işlemlerinde değişiklik ve iptale konu uçuş için satın alınmış Özel Hizmetlerin veya Paket içeriğinin yeni uçuşa taşınması veya iadesi hakkında aşağıda ilgili bölümde yer alan düzenlemeler geçerlidir.

5.1.7.    Taşıma Ücreti, (a) Baz Ücret, (b) Yakıt Harcı, (c) Havalimanı Vergisi, (d) Check-in Bedeli ve (e) Hizmet Bedelinden oluşmaktadır. Bilet değişiklik işlemlerinde, yeni biletin Taşıma Ücreti, eski biletin Taşıma Ücretinden yapılan kesinti sonrası kalan tutardan fazla ise aradaki ücret farkı tahsil edilir. Daha düşük bir ücrete değişiklik yapılması halinde sınıf ücret farkı iadesi yapılmaz. Bilet değişikliklerinde Avantaj Paket ve Comfort Flex Paket kapsamında düzenlenen Biletler için Paket içeriği yeni uçuşa taşınmamaktadır ve aradaki ücret farkı iade edilmektedir. Ek olarak, taksitli ödeme ile satın alınan Biletin değişiklik veya iptal işlemlerinde, tahsil edilen taksitlendirme bedeli yansıtılır.

5.1.8.    Bileti olduğu hâlde Tarifeli Uçuşa 2 saatten az zaman kala veya Tarifeli Uçuş saatinden sonra iptal talebinde bulunan misafirlerimiz uçuşa katılmamış yolcu (no show) olarak değerlendirilir. Bu hallerde, Bilet üzerinde değişiklik işlemi yapılmaz. İptal işlemi halinde ise sadece Havalimanı Vergisi misafire iade edilir, Baz Ücret, Yakıt Harcı, Check-in Bedeli ve Hizmet Bedeli yolcuya iade edilmez. İade talebinizi Biletinizi Pegasus İnternet Sitesi, Pegasus Mobil Uygulamaları, Pegasus Çağrı Merkezi veya Havalimanı Bilet Satış Ofislerinden aldıysanız, çevrimiçi satış kanallarının herhangi biri üzerinden “Bilet İşlemlerim” menü adımından PNR (Rezervasyon) Kodu ve soyadı bilgisi ile giriş yaparak veya “Bize Yazın” sayfasından ücretsiz olarak iletebilirsiniz. Biletinizi Acente üzerinden aldıysanız, iade işleminizi aynı kanal üzerinden gerçekleştirebilir veya çevrimiçi kanallarımız üzerinde yer alan “Bize Yazın” sayfasından ücretsiz olarak bize iletebilirsiniz. Talebiniz üzerine Biletinize ilişkin iade almaya hak ettiğiniz tutar hesaplanarak en geç 30 gün içerisinde işlem sonuçlandırılacaktır. 

5.1.9.    Bileti açığa alma talepleri Bilet değişikliği kapsamında değerlendirilir.

5.1.10.  Taksitle satın alınan biletlerde yapılan iptaller, ödeme yapılan kart hesabına tekrar taksitli olarak iade edilmektedir. Ek olarak, taksitli ödeme ile satın alınan Biletin değişiklik veya iptal işlemlerinde, tahsil edilen taksitlendirme bedeli yansıtılır.

* phone = *********** · birth_date = dd.MM.yyyy

## 5. Açılış ve Bilgi Toplama
Aşama	Erkan’ın Dediği
1	“Merhaba, Pegasus Havayollarına hoş geldiniz. İsmim Erkan. Sizlere nasıl yardımcı olabilirim?”
2	“Öncelikle hitap edebilmem için isminizi öğrenebilir miyim?”
3	“Soyadınızı alabilir miyim?” → Teyit: “{soyadı} soyismiyle işlemlerime devam ediyorum.”
4	“Lütfen altı haneli PNR numaranızı, harfleri şehir isimleriyle kodlayarak iletir misiniz?”
5	PNR yavaş ve anlaşılır şekilde tekrar edilir, sırayla seslendirilir → onay alınır → check_ticket

## 6. İptal Edilmiş Bilet Akışı
Adım	Eylem
6	Yolcu adı-soyadı istenir → teyit
7	Telefon numarası istenir → teyit
8	Doğum tarihi istenir → teyit
9	check_passenger
10	Bilgilendirme & Seçenek
“Teşekkürler. {flight_info} uçuş bilginize göre, {flight_date} tarihli ({departure_port} – {arrival_port}) seferiniz {cancellation_reason} nedeniyle iptal edilmiştir. İptal edilen uçuşunuz için tam iade, açık bilet veya uçuş değişikliği seçeneklerimiz var. Hangisini tercih edersiniz?”
11a	Açık bilet seçilirse →  call "transfer_call" function
11b	Uçuş değişikliği seçilirse → call "transfer_call" function
11c	Tam iade seçilirse → “Bilet iptal işlemi sonrasında hesabınıza yatacak iade tutarını 100 EUR olarak görüntülüyorum. Tam iade işlemi yapmak ister misiniz?”
12	Onay → cancel_ticket
13	Onaysız → nazik kapanış
14	“Size yardımcı olabileceğim başka bir konu var mı?”
14a	Kullanıcı yeni bir soru veya talepte bulunursa Erkan, ilgili konu hakkında yardımcı olur ve ardından tekrar “Size yardımcı olabileceğim başka bir konu var mı?” diye sorar.
14b	Kullanıcı “yok”, “teşekkürler”, “hayır” veya benzeri bir şekilde başka talebi olmadığını belirtirse Erkan teşekkür ve veda mesajı verir, ardından "end_call" fonksiyonunu çağırır.
15	Teşekkür & veda → end_call
**ÖNEMLİ: eğer müşteri biletinde uçuş değişikliği ve ya açık bilet seçerse "transfer_call" methodunu çağırıp canlı temsilciye aktarmalısın bu işlemleri sadece canlı temsilciler gerçekleştirebilir.**

## 7. Sesli Anlama Hataları – PNR ve Telefon Numarası
Erkan, müşteri tarafından verilen bilgilerde PNR veya telefon numarasını anlamakta zorlandığında aşağıdaki adımları uygular:

PNR Anlaşılamadıysa:
“Pienar numaranızı tam olarak anlayamadım. Lütfen tekrar, harfleri şehir isimleriyle kodlayarak iletebilir misiniz?”

Eğer ikinci denemede de anlaşılmazsa:

“İsterseniz harfleri biraz daha yavaş kodlayarak tekrar edebilir misiniz? Yardımcı olmaya hazırım.”

Telefon Numarası Anlaşılamadıysa:
“Telefon numaranızı tam olarak anlayamadım. Lütfen sıfırla başlayarak, üç haneli üç grup ve ardından iki haneli son grup olacak şekilde tekrar edebilir misiniz?”

Genel Uygulama:
Erkan, aldığı PNR ve telefon bilgisini yavaş ve sırayla yüksek sesle tekrar eder ve kullanıcıdan açık onay alır.

Yanlış anlama durumlarında daima nazik, sabırlı ve destekleyici bir dil kullanılır.

## 8. Dil ve Davranış Kuralları
“PNR” terimi daima “pienar” şeklinde seslendirilir.

Kullanıcının kodladığı PNR, harf ve sayı sırasıyla tekrar edilir; hiçbir ekleme, kısaltma ya da atlama yapılmaz.

PNR her zaman 6 haneli olmalıdır. Eksik ya da fazla hane varsa:

“Pienar numaranız 6 haneli olmalı. Lütfen kontrol edip tekrar kodlayabilir misiniz?”

Para birimleri yalnızca şu şekilde telaffuz edilir:

“EUR” → “yuro”

“TL” veya “TRY” → “türk lirası”

“Size yardımcı olabileceğim başka bir konu var mı?” sorusundan sonra kullanıcı yanıt vermeden veya en az 4–5 saniye geçmeden kapanış yapılmaz.

Erkan tüm konuşma boyunca nazik, kibar ve saygılı bir dil kullanır.

Hitaplarda mutlaka uygun ek kullanılır: “Emre Bey”, “Ayşe Hanım” gibi.

Müşteri mağduriyet yaşadığında Erkan mutlaka anlayış gösterir:

“Yaşadığınız aksaklık için gerçekten üzgünüm.”

Her durumda empati, sabır ve çözüm odaklı yaklaşım esastır. Müşteriye destek hissi verilmelidir.

Konuşma hiçbir zaman samimiyeti aşan, argo ya da laubali bir hale dönüşmez.

Kesinlikle pienar numarasını harf ya da rakam olarak kısaltarak (ör. “D-N-A-2-M-7”) veya başka bir formata dönüştürerek okumayacaksın.

Her zaman kullanıcının söylediği formatı (şehir ve rakam isimleriyle, araya başka kelime eklemeden ve sırayı değiştirmeden) tekrar edeceksin.`,
      });
    }
  }, [agentId, isLoading, tokens, user, setConfig]);

  return (
    <>
      <div className="flex flex-wrap gap-4">
        <div className="flex items-center">
          <AgentConfLlmSelect />

          {/* <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="rounded-l-none border-l-0">
                <Settings />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <p className="font-semibold text-sm">LLM Temperature</p>
              <p className="text-xs opacity-60">
                Lower value yields better function call results.
              </p>
              <Slider className="mt-2" defaultValue={[0]} max={1} step={0.01} />

              <p className="font-semibold text-sm mt-4">Structured Output</p>
              <p className="text-xs opacity-60">
                Always generate responses that adhere to your supplied JSON
                Schema.
              </p>
              <Switch className="mt-2" />

              <Separator className="my-4" />
              <div className="flex gap-2 justify-end">
                <Button variant="outline">Cancel</Button>
                <Button>Save</Button>
              </div>
            </PopoverContent>
          </Popover> */}
        </div>

        <div className="flex items-center">
          <Select
            value={JSON.stringify({
              provider: config.tts.provider,
              model: config.tts.model,
            })}
            onValueChange={(value) =>
              setConfig({
                ...config,
                tts: {
                  ...config.tts,
                  model: JSON.parse(value).model,
                  provider: JSON.parse(value).provider,
                  voiceid:
                    JSON.parse(value).provider === "openai"
                      ? "echo"
                      : JSON.parse(value).provider === "local"
                      ? "local"
                      : JSON.parse(value).provider === "elevenlabs"
                      ? "hzhgx3v7bdKdsVdWaE6h"
                      : "undefined",
                },
              })
            }
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {tts_models.map((model, i) => (
                  <SelectItem key={i} value={JSON.stringify(model.value)}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center">
          <Select
            value={JSON.stringify({
              provider: config.stt.provider,
              model: config.stt.model,
            })}
            onValueChange={(value) =>
              setConfig({
                ...config,
                stt: {
                  ...config.stt,
                  model: JSON.parse(value).model,
                  provider: JSON.parse(value).provider,
                },
              })
            }
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {stt_models.map((model, i) => (
                  <SelectItem key={i} value={JSON.stringify(model.value)}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center">
          <Select
            value={config.tts.voiceid}
            onValueChange={(value) =>
              setConfig({
                ...config,
                tts: {
                  ...config.tts,
                  voiceid: value,
                },
              })
            }
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {voices[config.tts.provider as keyof typeof voices].map(
                  (model, i) => (
                    <SelectItem key={i} value={model.value}>
                      {model.label}
                    </SelectItem>
                  )
                )}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center">
          <Select
            value={config.stt.language}
            onValueChange={(value) =>
              setConfig({
                ...config,
                stt: {
                  ...config.stt,
                  language: value,
                },
              })
            }
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="tr">Turkish</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2 ml-auto">
          <Drawer>
            <DrawerTrigger asChild className="2xl:hidden">
              <Button variant="outline">
                <Bolt />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="p-4">
              <DialogTitle className="sr-only">Agent Configuration</DialogTitle>
              <AgentConf />
              <DrawerFooter>
                <DrawerClose asChild className="ml-auto">
                  <Button variant="outline">Close</Button>
                </DrawerClose>
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
          <Drawer>
            <DrawerTrigger asChild className="2xl:hidden">
              <Button variant="outline">
                <TestTube />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="max-h-[50vh] p-4 h-full">
              <DialogTitle className="sr-only">Agent Test</DialogTitle>
              <AgentTest />
              <DrawerFooter>
                <DrawerClose asChild className="ml-auto">
                  <Button variant="outline">Close</Button>
                </DrawerClose>
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
        </div>
      </div>
      {(config.llm.provider === "local" ||
        config.tts.provider === "local" ||
        config.stt.provider === "local") && (
        <div className="flex gap-4 mt-3">
          {config.llm.provider === "local" && (
            <div className="flex items-center">
              <Input
                placeholder="Enter Local LLM Base Url"
                value={config.llm.baseurl}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    llm: {
                      ...config.llm,
                      baseurl: e.target.value,
                    },
                  })
                }
              />
            </div>
          )}

          {config.tts.provider === "local" && (
            <div className="flex items-center">
              <Input
                placeholder="Enter Local TTS Base Url"
                value={config.tts.baseurl}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    tts: {
                      ...config.tts,
                      baseurl: e.target.value,
                    },
                  })
                }
              />
            </div>
          )}

          {config.stt.provider === "local" && (
            <div className="flex items-center">
              <Input
                placeholder="Enter Local STT Base Url"
                value={config.stt.baseurl}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    stt: {
                      ...config.stt,
                      baseurl: e.target.value,
                    },
                  })
                }
              />
            </div>
          )}
        </div>
      )}

      <Textarea
        onChange={(e) =>
          setConfig({
            ...config,
            prompt: e.target.value,
          })
        }
        value={config.prompt}
        className="h-[60vh] mt-2"
      />
      <p className="font-semibold mt-2">Welcome message</p>
      <Select
        value={JSON.stringify({
          whoFirst: config.firstMessage.whoFirst,
          type: config.firstMessage.type,
        })}
        onValueChange={(value) => {
          console.log(value);
          setConfig({
            ...config,
            firstMessage: {
              ...config.firstMessage,
              whoFirst: JSON.parse(value).whoFirst,
              type: JSON.parse(value).type,
            },
          });
        }}
      >
        <SelectTrigger className="w-full mt-2">
          <SelectValue placeholder="Select welcome message" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              value={JSON.stringify({
                whoFirst: "customer",
              })}
            >
              User Initiates: AI remains silent until users speak first.
            </SelectItem>
            <SelectItem
              value={JSON.stringify({
                whoFirst: "agent",
                type: "static",
              })}
            >
              AI Initiates: AI begins with your defined begin message.
            </SelectItem>
            <SelectItem
              value={JSON.stringify({
                whoFirst: "agent",
                type: "prompt",
              })}
            >
              AI Initiates: AI begins with your defined begin prompt.
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
      {config.firstMessage.whoFirst === "agent" && (
        <>
          <Input
            placeholder="Enter custom welcome message"
            className="mt-2"
            value={config.firstMessage.text}
            onChange={(e) =>
              setConfig({
                ...config,
                firstMessage: {
                  ...config.firstMessage,
                  text: e.target.value,
                },
              })
            }
          />
          <Select
            value={config.firstMessage.allowinterruptions!.toString()}
            onValueChange={(value) =>
              setConfig({
                ...config,
                firstMessage: {
                  ...config.firstMessage,
                  allowinterruptions: value,
                },
              })
            }
          >
            <SelectTrigger className="w-full mt-2">
              <SelectValue placeholder="Select welcome message" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value={true.toString()}>
                  Allow Interruptions
                </SelectItem>
                <SelectItem value={false.toString()}>
                  Not Allow Interruptions
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </>
      )}
    </>
  );
};

export default AgentMain;
